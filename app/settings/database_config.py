"""
数据库配置管理
根据不同环境提供不同的 SQLite 优化参数
"""
import os
from typing import List


class DatabaseConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_sqlite_init_commands(environment: str = "production") -> List[str]:
        """
        根据环境获取 SQLite 初始化命令
        
        Args:
            environment: 环境类型 ("production", "development", "testing")
        
        Returns:
            SQLite PRAGMA 命令列表
        """
        
        # 基础配置（所有环境通用）
        base_commands = [
            # 启用 WAL 模式，提升并发性能
            "PRAGMA journal_mode=WAL;",
            # 启用外键约束
            "PRAGMA foreign_keys=ON;",
            # 设置临时存储在内存中
            "PRAGMA temp_store=MEMORY;",
            # 优化查询计划器
            "PRAGMA optimize;",
            # 设置忙等待超时为 30 秒
            "PRAGMA busy_timeout=30000;",
            # 启用递归触发器
            "PRAGMA recursive_triggers=ON;",
            # 设置 WAL 自动检查点
            "PRAGMA wal_autocheckpoint=1000;",
        ]
        
        # 根据环境添加特定配置
        if environment == "production":
            # 生产环境：安全优先，性能其次
            environment_commands = [
                # 平衡性能和安全性
                "PRAGMA synchronous=NORMAL;",
                # 32MB 内存缓存
                "PRAGMA cache_size=-32000;",
                # 256MB 内存映射
                "PRAGMA mmap_size=268435456;",
            ]
        
        elif environment == "development":
            # 开发环境：性能优先，可以牺牲一些安全性
            environment_commands = [
                # 您建议的高性能设置（牺牲安全性）
                "PRAGMA synchronous=OFF;",
                # 您建议的 10MB 缓存
                "PRAGMA cache_size=-10000;",
                # 更大的内存映射
                "PRAGMA mmap_size=536870912;",  # 512MB
            ]
        
        elif environment == "testing":
            # 测试环境：快速，但保持一定安全性
            environment_commands = [
                # 较快的同步模式
                "PRAGMA synchronous=NORMAL;",
                # 16MB 缓存
                "PRAGMA cache_size=-16000;",
                # 128MB 内存映射
                "PRAGMA mmap_size=134217728;",
            ]
        
        else:
            # 默认使用生产环境配置
            environment_commands = [
                "PRAGMA synchronous=NORMAL;",
                "PRAGMA cache_size=-32000;",
                "PRAGMA mmap_size=268435456;",
            ]
        
        return base_commands + environment_commands
    
    @staticmethod
    def get_performance_config(environment: str = "production") -> dict:
        """
        获取性能相关配置
        
        Args:
            environment: 环境类型
        
        Returns:
            性能配置字典
        """
        configs = {
            "production": {
                "connection_pool_size": 10,
                "query_timeout": 30,
                "slow_query_threshold": 1.0,
                "enable_query_logging": False,
            },
            "development": {
                "connection_pool_size": 5,
                "query_timeout": 60,
                "slow_query_threshold": 0.5,
                "enable_query_logging": True,
            },
            "testing": {
                "connection_pool_size": 3,
                "query_timeout": 10,
                "slow_query_threshold": 0.1,
                "enable_query_logging": True,
            }
        }
        
        return configs.get(environment, configs["production"])


def get_current_environment() -> str:
    """
    获取当前环境
    
    Returns:
        环境名称
    """
    # 从环境变量获取
    env = os.getenv("APP_ENV", "production").lower()
    
    # 也可以从 DEBUG 设置推断
    debug = os.getenv("DEBUG", "false").lower()
    if debug in ("true", "1", "yes") and env == "production":
        env = "development"
    
    return env


# 使用示例
if __name__ == "__main__":
    # 获取当前环境的配置
    current_env = get_current_environment()
    print(f"当前环境: {current_env}")
    
    # 获取 SQLite 配置
    sqlite_commands = DatabaseConfig.get_sqlite_init_commands(current_env)
    print("SQLite 配置:")
    for cmd in sqlite_commands:
        print(f"  {cmd}")
    
    # 获取性能配置
    perf_config = DatabaseConfig.get_performance_config(current_env)
    print(f"性能配置: {perf_config}")
