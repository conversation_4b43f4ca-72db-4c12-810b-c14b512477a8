[project]
name = "streamforge"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi==0.115.6",
    "uvicorn==0.34.0",
    "tortoise-orm[aiosqlite]==0.23.0",
    "pydantic==2.10.4",
    "pydantic-settings==2.7.0",
    "passlib==1.7.4",
    "pyjwt==2.10.1",
    "loguru==0.7.3",
    "aerich[toml]==0.8.1",
    "email-validator==2.2.0",
    "setuptools==75.6.0",
    "argon2-cffi==23.1.0",
    "fastapi-cache2==0.2.2",
    "redis==5.2.1",
    "orjson==3.10.13",
    "alibabacloud-dysmsapi20170525>=3.1.0",
    "aiosqlite==0.20.0",
    "annotated-types==0.7.0",
    "anyio==4.7.0",
    "argon2-cffi-bindings==21.2.0",
    "async-timeout==5.0.1 ; python_full_version < '3.11.3'",
    "asyncclick==*******",
    "cffi==1.17.1",
    "click==8.1.8",
    "colorama==0.4.6 ; sys_platform == 'win32'",
    "dictdiffer==0.9.0",
    "dnspython==2.7.0",
    "exceptiongroup==1.2.2 ; python_full_version < '3.11'",
    "h11==0.14.0",
    "idna==3.10",
    "iso8601==2.1.0",
    "pendulum==3.0.0",
    "pycparser==2.22",
    "pydantic-core==2.27.2",
    "pypika-tortoise==0.3.2",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.1",
    "pytz==2024.2",
    "six==1.17.0",
    "sniffio==1.3.1",
    "starlette==0.41.3",
    "time-machine==2.16.0 ; implementation_name != 'pypy'",
    "typing-extensions==4.12.2",
    "tzdata==2024.2",
    "win32-setctime==1.2.0 ; sys_platform == 'win32'",
    "aiofiles",
]
license ={ text = "MIT" }

[tool.ruff]
line-length = 120

[tool.ruff.lint]
exclude = ["web", "node_modules", "migrations", "src/experimental", "src/typestubs", "src/oldstuff"]
extend-select = [
    "F",
    "E",
    "W",
    "UP",
    # "I",    # isort
    # "B",    # flake8-bugbear
    # "C4",   # flake8-comprehensions
    # "PGH",  # pygrep-hooks
    # "RUF",  # ruff
    # "W",    # pycodestyle
    # "YTT",  # flake8-2020
]

ignore = [
    "F403",
    "F405",
    "E501", # Line too long
    #    "I001", # isort
    "W293",
]

[tool.ruff.format]
#quote-style = "single"
#indent-style = "tab"
#docstring-code-format = true

[tool.pyright]
include = ["app"]
exclude = ["web",
    "**/node_modules",
    "**/__pycache__",
    "src/experimental",
    "src/typestubs",
]

ignore = [
    "migrations", "src/oldstuff",
]

defineConstant = { DEBUG = false }
stubPath = "src/stubs"

reportMissingImports = true
#reportMissingTypeStubs = false
#
#reportIncompatibleVariableOverride = false

[tool.pdm]
distribution = false

[tool.pdm.build]
includes = []

[[tool.pdm.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
verify_ssl = true

[tool.aerich]
tortoise_orm = "app.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[tool.poetry]
name = "StreamForge"
version = "0.1.0"
description = ""
authors = ["jiqinga <<EMAIL>>"]
readme = "README.md"
include = ["StreamForge/**/*"]
[tool.poetry.dependencies]
python = "^3.11"
chardet = "^5.2.0"
sse-starlette = "^1.6.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/simple"
default = true
